{"name": "timetable-frontend", "version": "1.0.0", "description": "语音排课系统前端", "private": true, "type": "module", "dependencies": {"antd": "^5.10.0", "axios": "^1.5.0", "dayjs": "^1.11.9", "html2canvas": "^1.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "web-vitals": "^2.1.4"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@vitejs/plugin-react": "^4.0.3", "vite": "^4.4.5"}, "scripts": {"dev": "vite --mode development", "preview": "vite preview", "build": "vite build", "serve": "vite preview", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}