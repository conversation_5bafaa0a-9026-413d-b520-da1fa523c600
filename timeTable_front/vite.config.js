import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'

// 定义API主机常量
const LOCAL_API_HOST = 'http://localhost:8088';
const REMOTE_API_HOST = 'http://timetable.devtesting.top'; // 可根据实际情况修改

// 配置代理日志记录函数
const configureProxyLogs = (proxy) => {
  proxy.on('error', (err, req, res) => {
    console.error('代理错误:', err);
  });
  proxy.on('proxyReq', (proxyReq, req, res) => {
    console.log('代理请求:', req.method, req.url);
  });
  proxy.on('proxyRes', (proxyRes, req, res) => {
    console.log('代理响应:', proxyRes.statusCode, req.url);
  });
};

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '');
  
  // 根据命令和模式选择API主机
  const isDev = command === 'serve' && mode === 'development';
  const isPreview = command === 'serve' && mode === 'production';
  const API_HOST = isDev ? LOCAL_API_HOST : REMOTE_API_HOST;
  
  console.log(`当前命令: ${command}, 模式: ${mode}, API主机: ${API_HOST}`);
  
  // 为preview模式创建特殊配置
  const serverConfig = {
    port: 3000,
    host: true,
    open: true,
    cors: true,
    proxy: {
      '/api': {
        target: API_HOST,
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, ''),
        configure: configureProxyLogs
      },
      '/timetable/api': {
        target: API_HOST,
        changeOrigin: true,
        secure: false,
        configure: configureProxyLogs
      },
      '/remote': {
        target: REMOTE_API_HOST,
        changeOrigin: true,
        secure: false,
        rewrite: (path) => path.replace(/^\/remote/, ''),
        headers: {
          'Referer': REMOTE_API_HOST,
          'Origin': REMOTE_API_HOST
        },
        configure: configureProxyLogs
      },
    },
  };
  
  // 如果是preview模式，使用特殊的代理配置
  if (isPreview) {
    serverConfig.proxy = {
      '/timetable/api': {
        target: REMOTE_API_HOST,
        changeOrigin: true,
        secure: false,
        configure: configureProxyLogs
      },
    };
  }
  
  return {
    plugins: [react()],
    base: './', // 使用相对路径，支持直接打开index.html
    build: {
      outDir: 'dist',
      assetsDir: 'assets',
      // 生成相对路径的资源引用
      rollupOptions: {
        output: {
          manualChunks: undefined,
        },
      },
    },
    server: serverConfig,
    preview: {
      port: 3000,
      host: true,
      proxy: {
        '/timetable/api': {
          target: REMOTE_API_HOST,
          changeOrigin: true,
          secure: false,
          configure: configureProxyLogs
        },
      },
    },
  }
}) 